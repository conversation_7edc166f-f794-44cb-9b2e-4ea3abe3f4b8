<script setup>
import { ref, computed, watch } from "vue";
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const selectedFilter = ref('all');
const searchMode = ref('text'); // 'text' or 'coordinates'
const coordinateForm = ref({
    latitude: '',
    longitude: '',
});
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const showFilters = ref(false);

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);



// Feature card data
const features = ref([
    {
        title: "Real-Time Tracking",
        description: "Track assets with high-precision GPS for instant location updates.",
        list: [
            "Low-latency updates",
            "High-precision tracking",
            "Real-time monitoring"
        ],
        image: "/images/tracking.jpg"
    },
    {
        title: "Geofencing Precision",
        description: "Set accurate boundaries using Rwanda’s administrative data.",
        list: [
            "Less than 1m accuracy",
            "Complete boundary data",
            "Customizable zones"
        ],
        image: "/images/geofencing.jpg"
    }
]);

// Configuration
const languages = [
    { code: 'rw', name: 'Kinyarwanda', flag: '🇷🇼' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
];

const filters = [
    { code: 'all', name: 'All Locations', icon: '🌍' },
    { code: 'province', name: 'Provinces', icon: '🏛️' },
    { code: 'district', name: 'Districts', icon: '🏢' },
    { code: 'sector', name: 'Sectors', icon: '🏘️' },
    { code: 'cell', name: 'Cells', icon: '🏠' },
    { code: 'village', name: 'Villages', icon: '🏡' },
    { code: 'health_fac', name: 'Health Facilities', icon: '🏥' },
];

// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu mu Rwanda...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux au Rwanda...',
}[selectedLanguage.value] || 'Search locations...'));

// --- Functions ---
const performSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });

        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const performCoordinateSearch = debounce(async (lat, lng) => {
    if (!lat || !lng) return;

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/map/search-latitude-longitude-json', {
            latitude: parseFloat(lat),
            longitude: parseFloat(lng),
        });

        // Handle coordinate search results
        searchResults.value = data;
    } catch (err) {
        console.error('Coordinate search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch coordinate results.';
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) {
        searchQuery.value = '';
        coordinateForm.value = { latitude: '', longitude: '' };
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};

const getResultType = (type) => {
    return type.slice(0, -1); // Remove 's' from plural
};

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    if (searchMode.value === 'text') {
        performSearch(newQuery, selectedLanguage.value, selectedFilter.value);
    }
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim() && searchMode.value === 'text') {
        performSearch(searchQuery.value, newLang, selectedFilter.value);
    }
});

watch(selectedFilter, (newFilter) => {
    if (searchQuery.value.trim() && searchMode.value === 'text') {
        performSearch(searchQuery.value, selectedLanguage.value, newFilter);
    }
});

watch([() => coordinateForm.value.latitude, () => coordinateForm.value.longitude], ([lat, lng]) => {
    if (searchMode.value === 'coordinates' && lat && lng) {
        performCoordinateSearch(lat, lng);
    }
});
</script>

<template>
    <Head title="Rwanda Geo - Professional Geocoding Platform" />
    <AppLayout>
        <!-- Main Container -->
        <div class="min-h-screen bg-white">
            <!-- Hero Section with Search -->
            <section class="relative bg-gradient-to-br from-gray-50 to-white py-20 px-4 sm:px-6 lg:px-8">
                <!-- Background Pattern -->
                <div class="absolute inset-0 overflow-hidden">
                    <svg class="absolute top-0 left-0 w-full h-full opacity-5" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="black" stroke-width="0.5"/>
                            </pattern>
                        </defs>
                        <rect width="100" height="100" fill="url(#grid)" />
                    </svg>
                </div>

                <div class="relative max-w-7xl mx-auto">
                    <!-- Header -->
                    <div class="text-center mb-16">
                        <div class="flex justify-center mb-6">
                            <!-- Rwanda Map Icon -->
                            <div class="relative">
                                <svg class="w-20 h-20 text-black" viewBox="0 0 100 100" fill="currentColor">
                                    <path d="M20,30 Q30,20 50,25 Q70,20 80,30 L85,50 Q80,70 70,75 Q50,80 30,75 Q20,70 15,50 Z" stroke="currentColor" stroke-width="2" fill="none"/>
                                    <circle cx="35" cy="40" r="2" fill="currentColor"/>
                                    <circle cx="50" cy="35" r="2" fill="currentColor"/>
                                    <circle cx="65" cy="45" r="2" fill="currentColor"/>
                                </svg>
                                <!-- Animated pulse -->
                                <div class="absolute inset-0 rounded-full border-2 border-black animate-ping opacity-20"></div>
                            </div>
                        </div>
                        <h1 class="text-5xl md:text-6xl font-bold text-black mb-6 tracking-tight">
                            Rwanda Geo
                        </h1>
                        <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                            Professional geocoding platform for Rwanda's administrative boundaries and locations
                        </p>

                        <!-- Trust Indicators -->
                        <div class="flex flex-wrap justify-center gap-6 mb-12">
                            <Badge variant="outline" class="px-4 py-2 text-sm font-medium border-black text-black rounded-full">
                                🎯 Sub-meter Accuracy
                            </Badge>
                            <Badge variant="outline" class="px-4 py-2 text-sm font-medium border-black text-black rounded-full">
                                ⚡ Real-time API
                            </Badge>
                            <Badge variant="outline" class="px-4 py-2 text-sm font-medium border-black text-black rounded-full">
                                🏛️ Official Data
                            </Badge>
                        </div>
                    </div>

                    <!-- Search Interface -->
                    <div class="max-w-4xl mx-auto">
                        <Card class="border-2 border-black rounded-3xl shadow-lg bg-white">
                            <CardHeader class="pb-4">
                                <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                                    <CardTitle class="text-2xl font-bold text-black">Search Rwanda</CardTitle>
                                    <div class="flex gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            @click="showFilters = !showFilters"
                                            class="border-black text-black hover:bg-black hover:text-white rounded-full"
                                        >
                                            🔧 Filters
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent class="space-y-6">
                                <!-- Search Mode Tabs -->
                                <Tabs v-model="searchMode" class="w-full">
                                    <TabsList class="grid w-full grid-cols-2 bg-gray-100 rounded-2xl p-1">
                                        <TabsTrigger value="text" class="rounded-xl font-medium">🔍 Text Search</TabsTrigger>
                                        <TabsTrigger value="coordinates" class="rounded-xl font-medium">📍 Coordinates</TabsTrigger>
                                    </TabsList>

                                    <!-- Text Search -->
                                    <TabsContent value="text" class="space-y-4 mt-6">
                                        <div class="relative">
                                            <Input
                                                v-model="searchQuery"
                                                :placeholder="getPlaceholderText"
                                                class="h-14 pl-12 pr-4 text-lg border-2 border-gray-200 rounded-2xl focus:border-black focus:ring-0"
                                            />
                                            <svg class="absolute left-4 top-1/2 -translate-y-1/2 h-6 w-6 text-gray-400"
                                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                            </svg>
                                        </div>
                                    </TabsContent>

                                    <!-- Coordinate Search -->
                                    <TabsContent value="coordinates" class="space-y-4 mt-6">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Latitude</label>
                                                <Input
                                                    v-model="coordinateForm.latitude"
                                                    placeholder="-1.9403"
                                                    type="number"
                                                    step="any"
                                                    class="h-12 border-2 border-gray-200 rounded-2xl focus:border-black focus:ring-0"
                                                />
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Longitude</label>
                                                <Input
                                                    v-model="coordinateForm.longitude"
                                                    placeholder="29.8739"
                                                    type="number"
                                                    step="any"
                                                    class="h-12 border-2 border-gray-200 rounded-2xl focus:border-black focus:ring-0"
                                                />
                                            </div>
                                        </div>
                                    </TabsContent>
                                </Tabs>

                                <!-- Filters Panel -->
                                <div v-if="showFilters" class="space-y-4 p-4 bg-gray-50 rounded-2xl border-2 border-gray-200">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <!-- Language Filter -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                                            <Select v-model="selectedLanguage">
                                                <SelectTrigger class="h-12 border-2 border-gray-200 rounded-2xl">
                                                    <SelectValue placeholder="Select language" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="lang in languages" :key="lang.code" :value="lang.code">
                                                        {{ lang.flag }} {{ lang.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <!-- Location Type Filter -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Location Type</label>
                                            <Select v-model="selectedFilter">
                                                <SelectTrigger class="h-12 border-2 border-gray-200 rounded-2xl">
                                                    <SelectValue placeholder="Select type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="filter in filters" :key="filter.code" :value="filter.code">
                                                        {{ filter.icon }} {{ filter.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Search Status -->
                                <div v-if="isLoading || hasResults || error" class="space-y-4">
                                    <!-- Loading State -->
                                    <div v-if="isLoading" class="flex items-center justify-center py-8">
                                        <div class="flex items-center space-x-3">
                                            <div class="animate-spin rounded-full h-6 w-6 border-2 border-black border-t-transparent"></div>
                                            <span class="text-gray-600">Searching...</span>
                                        </div>
                                    </div>

                                    <!-- Error State -->
                                    <div v-if="error" class="p-4 bg-red-50 border-2 border-red-200 rounded-2xl">
                                        <p class="text-red-700 font-medium">{{ error }}</p>
                                    </div>

                                    <!-- Results -->
                                    <div v-if="hasResults && !isLoading" class="space-y-4">
                                        <div class="flex items-center justify-between">
                                            <h3 class="text-lg font-semibold text-black">
                                                Found {{ totalResults }} location{{ totalResults !== 1 ? 's' : '' }}
                                            </h3>
                                            <Badge variant="outline" class="text-xs border-gray-300">
                                                {{ searchTime }}ms
                                            </Badge>
                                        </div>

                                        <div class="max-h-96 overflow-y-auto space-y-2">
                                            <template v-for="(type, typeKey) in searchResults" :key="typeKey">
                                                <div v-if="type.length > 0" class="space-y-2">
                                                    <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                                                        {{ getResultType(typeKey) }}s ({{ type.length }})
                                                    </h4>
                                                    <div v-for="result in type.slice(0, 5)" :key="result.id"
                                                         class="p-3 bg-white border border-gray-200 rounded-xl hover:border-black transition-colors cursor-pointer">
                                                        <div class="flex items-center justify-between">
                                                            <div>
                                                                <p class="font-medium text-black">{{ getDisplayName(result) }}</p>
                                                                <p class="text-sm text-gray-500">
                                                                    {{ result.latitude?.toFixed(6) }}, {{ result.longitude?.toFixed(6) }}
                                                                </p>
                                                            </div>
                                                            <Badge variant="outline" class="text-xs">
                                                                {{ getResultType(typeKey) }}
                                                            </Badge>
                                                        </div>
                                                    </div>
                                                    <div v-if="type.length > 5" class="text-center">
                                                        <Button variant="ghost" size="sm" class="text-gray-500">
                                                            +{{ type.length - 5 }} more
                                                        </Button>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
                <div class="max-w-7xl mx-auto">
                    <div class="text-center mb-16">
                        <h2 class="text-4xl font-bold text-black mb-6">Why Choose Rwanda Geo?</h2>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            Built for developers, trusted by enterprises. Our platform delivers precision and reliability.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <!-- Feature 1 -->
                        <Card class="border-2 border-gray-200 rounded-3xl hover:border-black transition-colors bg-white">
                            <CardHeader class="text-center pb-4">
                                <div class="w-16 h-16 mx-auto mb-4 bg-black rounded-2xl flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <CardTitle class="text-xl font-bold text-black">Lightning Fast</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p class="text-gray-600 text-center">
                                    Sub-100ms response times with our optimized search algorithms and caching infrastructure.
                                </p>
                            </CardContent>
                        </Card>

                        <!-- Feature 2 -->
                        <Card class="border-2 border-gray-200 rounded-3xl hover:border-black transition-colors bg-white">
                            <CardHeader class="text-center pb-4">
                                <div class="w-16 h-16 mx-auto mb-4 bg-black rounded-2xl flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <CardTitle class="text-xl font-bold text-black">Precision Accuracy</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p class="text-gray-600 text-center">
                                    Sub-meter accuracy using official government data and advanced geospatial processing.
                                </p>
                            </CardContent>
                        </Card>

                        <!-- Feature 3 -->
                        <Card class="border-2 border-gray-200 rounded-3xl hover:border-black transition-colors bg-white">
                            <CardHeader class="text-center pb-4">
                                <div class="w-16 h-16 mx-auto mb-4 bg-black rounded-2xl flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <CardTitle class="text-xl font-bold text-black">Enterprise Ready</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p class="text-gray-600 text-center">
                                    99.9% uptime SLA, comprehensive API documentation, and dedicated support.
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            <!-- CTA Section -->
            <section class="py-20 px-4 sm:px-6 lg:px-8 bg-black">
                <div class="max-w-4xl mx-auto text-center">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                        Ready to Get Started?
                    </h2>
                    <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                        Join hundreds of developers and companies using Rwanda Geo for their location intelligence needs.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" class="bg-white text-black hover:bg-gray-100 rounded-2xl px-8 py-4 text-lg font-medium">
                            🚀 Start Building
                        </Button>
                        <Button variant="outline" size="lg" class="border-white text-white hover:bg-white hover:text-black rounded-2xl px-8 py-4 text-lg font-medium">
                            📖 View Documentation
                        </Button>
                    </div>
                </div>
            </section>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Custom animations and styles */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>