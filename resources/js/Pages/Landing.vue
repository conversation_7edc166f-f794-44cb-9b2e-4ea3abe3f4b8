<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, MapPin, Globe, Building, Home, Hospital, Settings, Zap, Shield, CheckCircle } from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const selectedFilter = ref('all');
const searchMode = ref('text'); // 'text' or 'coordinates'
const coordinateForm = ref({
    latitude: '',
    longitude: '',
});
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const showFilters = ref(false);

// Mouse tracking for interactive character
const mousePosition = ref({ x: 0, y: 0 });
const isTyping = ref(false);
const eyeDirection = ref({ x: 0, y: 0 });
const headRotation = ref(0);

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);



// Feature card data
const features = ref([
    {
        title: "Real-Time Tracking",
        description: "Track assets with high-precision GPS for instant location updates.",
        list: [
            "Low-latency updates",
            "High-precision tracking",
            "Real-time monitoring"
        ],
        image: "/images/tracking.jpg"
    },
    {
        title: "Geofencing Precision",
        description: "Set accurate boundaries using Rwanda’s administrative data.",
        list: [
            "Less than 1m accuracy",
            "Complete boundary data",
            "Customizable zones"
        ],
        image: "/images/geofencing.jpg"
    }
]);

// Configuration
const languages = [
    { code: 'rw', name: 'Kinyarwanda' },
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
];

const filters = [
    { code: 'all', name: 'All Locations', icon: Globe },
    { code: 'province', name: 'Provinces', icon: Building },
    { code: 'district', name: 'Districts', icon: Building },
    { code: 'sector', name: 'Sectors', icon: Home },
    { code: 'cell', name: 'Cells', icon: Home },
    { code: 'village', name: 'Villages', icon: Home },
    { code: 'health_fac', name: 'Health Facilities', icon: Hospital },
];

// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu mu Rwanda...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux au Rwanda...',
}[selectedLanguage.value] || 'Search locations...'));

// --- Functions ---
const performSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });

        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const performCoordinateSearch = debounce(async (lat, lng) => {
    if (!lat || !lng) return;

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/map/search-latitude-longitude-json', {
            latitude: parseFloat(lat),
            longitude: parseFloat(lng),
        });

        // Handle coordinate search results
        searchResults.value = data;
    } catch (err) {
        console.error('Coordinate search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch coordinate results.';
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) {
        searchQuery.value = '';
        coordinateForm.value = { latitude: '', longitude: '' };
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};

const getResultType = (type) => {
    return type.slice(0, -1); // Remove 's' from plural
};

// Mouse tracking functions
const updateMousePosition = (event) => {
    mousePosition.value = { x: event.clientX, y: event.clientY };

    // Calculate eye direction based on mouse position
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const deltaX = (event.clientX - centerX) / centerX;
    const deltaY = (event.clientY - centerY) / centerY;

    eyeDirection.value = {
        x: Math.max(-1, Math.min(1, deltaX * 0.3)),
        y: Math.max(-1, Math.min(1, deltaY * 0.3))
    };

    // Calculate head rotation
    headRotation.value = deltaX * 5; // Max 5 degrees rotation
};

const handleTyping = () => {
    isTyping.value = true;
    setTimeout(() => {
        isTyping.value = false;
    }, 1000);
};

// --- Lifecycle ---
onMounted(() => {
    document.addEventListener('mousemove', updateMousePosition);
});

onUnmounted(() => {
    document.removeEventListener('mousemove', updateMousePosition);
});

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    handleTyping();
    if (searchMode.value === 'text') {
        performSearch(newQuery, selectedLanguage.value, selectedFilter.value);
    }
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim() && searchMode.value === 'text') {
        performSearch(searchQuery.value, newLang, selectedFilter.value);
    }
});

watch(selectedFilter, (newFilter) => {
    if (searchQuery.value.trim() && searchMode.value === 'text') {
        performSearch(searchQuery.value, selectedLanguage.value, newFilter);
    }
});

watch([() => coordinateForm.value.latitude, () => coordinateForm.value.longitude], ([lat, lng]) => {
    if (searchMode.value === 'coordinates' && lat && lng) {
        performCoordinateSearch(lat, lng);
    }
});
</script>

<template>
    <Head title="Rwanda Geo - Professional Geocoding Platform" />
    <AppLayout>
        <!-- Main Container -->
        <div class="min-h-screen bg-white">
            <!-- Hero Section with Interactive Character and Search -->
            <section class="relative bg-gradient-to-br from-gray-50 to-white py-20 px-4 sm:px-6 lg:px-8">
                <!-- Background Pattern -->
                <div class="absolute inset-0 overflow-hidden">
                    <svg class="absolute top-0 left-0 w-full h-full opacity-5" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="black" stroke-width="0.5"/>
                            </pattern>
                        </defs>
                        <rect width="100" height="100" fill="url(#grid)" />
                    </svg>
                </div>

                <div class="relative max-w-7xl mx-auto">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center">
                        <!-- Interactive Character -->
                        <div class="flex justify-center lg:justify-start">
                            <div class="relative">
                                <svg
                                    class="w-32 h-32 text-black transition-transform duration-300 ease-out"
                                    :style="{ transform: `rotate(${headRotation}deg)` }"
                                    viewBox="0 0 100 100"
                                    fill="none"
                                >
                                    <!-- Head -->
                                    <circle cx="50" cy="40" r="25" stroke="currentColor" stroke-width="2" fill="white"/>

                                    <!-- Eyes -->
                                    <circle
                                        :cx="42 + eyeDirection.x * 2"
                                        :cy="35 + eyeDirection.y * 2"
                                        r="3"
                                        fill="currentColor"
                                        class="transition-all duration-200"
                                    />
                                    <circle
                                        :cx="58 + eyeDirection.x * 2"
                                        :cy="35 + eyeDirection.y * 2"
                                        r="3"
                                        fill="currentColor"
                                        class="transition-all duration-200"
                                    />

                                    <!-- Mouth -->
                                    <path
                                        :d="isTyping ? 'M 45 48 Q 50 52 55 48' : 'M 45 48 Q 50 45 55 48'"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        fill="none"
                                        class="transition-all duration-300"
                                    />

                                    <!-- Body -->
                                    <rect x="40" y="65" width="20" height="30" stroke="currentColor" stroke-width="2" fill="white" rx="10"/>
                                </svg>

                                <!-- Floating elements around character -->
                                <div class="absolute -top-4 -right-4 animate-bounce">
                                    <MapPin class="w-6 h-6 text-black" />
                                </div>
                                <div class="absolute -bottom-2 -left-4 animate-pulse">
                                    <Globe class="w-5 h-5 text-gray-600" />
                                </div>
                            </div>
                        </div>

                        <!-- Main Content -->
                        <div class="text-center lg:text-left">
                            <h1 class="text-4xl md:text-5xl font-bold text-black mb-6 tracking-tight">
                                Rwanda Geo
                            </h1>
                            <p class="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed">
                                Professional geocoding platform for Rwanda's administrative boundaries and locations
                            </p>

                            <!-- Trust Indicators -->
                            <div class="flex flex-wrap justify-center lg:justify-start gap-4 mb-8">
                                <Badge variant="outline" class="px-3 py-1 text-sm font-medium border-black text-black rounded-full flex items-center gap-2">
                                    <Zap class="w-4 h-4" />
                                    Sub-meter Accuracy
                                </Badge>
                                <Badge variant="outline" class="px-3 py-1 text-sm font-medium border-black text-black rounded-full flex items-center gap-2">
                                    <CheckCircle class="w-4 h-4" />
                                    Real-time API
                                </Badge>
                                <Badge variant="outline" class="px-3 py-1 text-sm font-medium border-black text-black rounded-full flex items-center gap-2">
                                    <Shield class="w-4 h-4" />
                                    Official Data
                                </Badge>
                            </div>
                        </div>

                        <!-- Search Interface -->
                        <div class="lg:col-span-1">
                            <Card class="border-2 border-black rounded-3xl shadow-lg bg-white">
                                <CardHeader class="pb-4">
                                    <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                                        <CardTitle class="text-xl font-bold text-black">Search Rwanda</CardTitle>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            @click="showFilters = !showFilters"
                                            class="border-black text-black hover:bg-black hover:text-white rounded-full flex items-center gap-2"
                                        >
                                            <Settings class="w-4 h-4" />
                                            Filters
                                        </Button>
                                    </div>
                                </CardHeader>
                                <CardContent class="space-y-6">
                                    <!-- Search Mode Tabs -->
                                    <Tabs v-model="searchMode" class="w-full">
                                        <TabsList class="grid w-full grid-cols-2 bg-gray-100 rounded-2xl p-1">
                                            <TabsTrigger value="text" class="rounded-xl font-medium flex items-center gap-2">
                                                <Search class="w-4 h-4" />
                                                Text Search
                                            </TabsTrigger>
                                            <TabsTrigger value="coordinates" class="rounded-xl font-medium flex items-center gap-2">
                                                <MapPin class="w-4 h-4" />
                                                Coordinates
                                            </TabsTrigger>
                                        </TabsList>

                                        <!-- Text Search -->
                                        <TabsContent value="text" class="space-y-4 mt-6">
                                            <div class="relative">
                                                <Input
                                                    v-model="searchQuery"
                                                    :placeholder="getPlaceholderText"
                                                    class="h-14 pl-12 pr-4 text-lg border-2 border-gray-200 rounded-2xl focus:border-black focus:ring-0"
                                                />
                                                <Search class="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                            </div>
                                        </TabsContent>

                                        <!-- Coordinate Search -->
                                        <TabsContent value="coordinates" class="space-y-4 mt-6">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Latitude</label>
                                                    <Input
                                                        v-model="coordinateForm.latitude"
                                                        placeholder="-1.9403"
                                                        type="number"
                                                        step="any"
                                                        class="h-12 border-2 border-gray-200 rounded-2xl focus:border-black focus:ring-0"
                                                    />
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Longitude</label>
                                                    <Input
                                                        v-model="coordinateForm.longitude"
                                                        placeholder="29.8739"
                                                        type="number"
                                                        step="any"
                                                        class="h-12 border-2 border-gray-200 rounded-2xl focus:border-black focus:ring-0"
                                                    />
                                                </div>
                                            </div>
                                        </TabsContent>
                                    </Tabs>

                                    <!-- Filters Panel -->
                                    <div v-if="showFilters" class="space-y-4 p-4 bg-gray-50 rounded-2xl border-2 border-gray-200">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <!-- Language Filter -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                                                <Select v-model="selectedLanguage">
                                                    <SelectTrigger class="h-12 border-2 border-gray-200 rounded-2xl">
                                                        <SelectValue placeholder="Select language" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem v-for="lang in languages" :key="lang.code" :value="lang.code">
                                                            {{ lang.name }}
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <!-- Location Type Filter -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Location Type</label>
                                                <Select v-model="selectedFilter">
                                                    <SelectTrigger class="h-12 border-2 border-gray-200 rounded-2xl">
                                                        <SelectValue placeholder="Select type" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem v-for="filter in filters" :key="filter.code" :value="filter.code" class="flex items-center gap-2">
                                                            <component :is="filter.icon" class="w-4 h-4" />
                                                            {{ filter.name }}
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Search Status -->
                                    <div v-if="isLoading || hasResults || error" class="space-y-4">
                                        <!-- Loading State -->
                                        <div v-if="isLoading" class="flex items-center justify-center py-6">
                                            <div class="flex items-center space-x-3">
                                                <div class="animate-spin rounded-full h-5 w-5 border-2 border-black border-t-transparent"></div>
                                                <span class="text-gray-600">Searching...</span>
                                            </div>
                                        </div>

                                        <!-- Error State -->
                                        <div v-if="error" class="p-3 bg-red-50 border border-red-200 rounded-xl">
                                            <p class="text-red-700 text-sm">{{ error }}</p>
                                        </div>

                                        <!-- Results -->
                                        <div v-if="hasResults && !isLoading" class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-sm font-semibold text-black">
                                                    {{ totalResults }} location{{ totalResults !== 1 ? 's' : '' }}
                                                </h3>
                                                <Badge variant="outline" class="text-xs border-gray-300">
                                                    {{ searchTime }}ms
                                                </Badge>
                                            </div>

                                            <div class="max-h-64 overflow-y-auto space-y-2">
                                                <template v-for="(type, typeKey) in searchResults" :key="typeKey">
                                                    <div v-if="type.length > 0">
                                                        <div v-for="result in type.slice(0, 3)" :key="result.id"
                                                             class="p-3 bg-white border border-gray-200 rounded-xl hover:border-black transition-colors cursor-pointer">
                                                            <div class="space-y-1">
                                                                <div class="flex items-center justify-between">
                                                                    <p class="font-medium text-black text-sm">{{ getDisplayName(result) }}</p>
                                                                    <Badge variant="outline" class="text-xs">
                                                                        {{ getResultType(typeKey) }}
                                                                    </Badge>
                                                                </div>
                                                                <p v-if="result.address" class="text-xs text-gray-600">
                                                                    📍 {{ result.address }}
                                                                </p>
                                                                <p class="text-xs text-gray-500">
                                                                    {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div v-if="type.length > 3" class="text-center">
                                                            <Button variant="ghost" size="sm" class="text-gray-500 text-xs">
                                                                +{{ type.length - 3 }} more {{ getResultType(typeKey) }}s
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
                <div class="max-w-6xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold text-black mb-4">Why Choose Rwanda Geo?</h2>
                        <p class="text-lg text-gray-600">
                            Built for developers, trusted by enterprises.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Feature 1 -->
                        <Card class="border-2 border-gray-200 rounded-2xl hover:border-black transition-colors bg-white">
                            <CardHeader class="text-center pb-3">
                                <div class="w-12 h-12 mx-auto mb-3 bg-black rounded-xl flex items-center justify-center">
                                    <Zap class="w-6 h-6 text-white" />
                                </div>
                                <CardTitle class="text-lg font-bold text-black">Lightning Fast</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p class="text-gray-600 text-center text-sm">
                                    Sub-100ms response times with optimized search algorithms.
                                </p>
                            </CardContent>
                        </Card>

                        <!-- Feature 2 -->
                        <Card class="border-2 border-gray-200 rounded-2xl hover:border-black transition-colors bg-white">
                            <CardHeader class="text-center pb-3">
                                <div class="w-12 h-12 mx-auto mb-3 bg-black rounded-xl flex items-center justify-center">
                                    <CheckCircle class="w-6 h-6 text-white" />
                                </div>
                                <CardTitle class="text-lg font-bold text-black">Precision Accuracy</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p class="text-gray-600 text-center text-sm">
                                    Sub-meter accuracy using official government data.
                                </p>
                            </CardContent>
                        </Card>

                        <!-- Feature 3 -->
                        <Card class="border-2 border-gray-200 rounded-2xl hover:border-black transition-colors bg-white">
                            <CardHeader class="text-center pb-3">
                                <div class="w-12 h-12 mx-auto mb-3 bg-black rounded-xl flex items-center justify-center">
                                    <Shield class="w-6 h-6 text-white" />
                                </div>
                                <CardTitle class="text-lg font-bold text-black">Enterprise Ready</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p class="text-gray-600 text-center text-sm">
                                    99.9% uptime SLA and comprehensive API documentation.
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </section>

            <!-- CTA Section -->
            <section class="py-16 px-4 sm:px-6 lg:px-8 bg-black">
                <div class="max-w-4xl mx-auto text-center">
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                        Ready to Get Started?
                    </h2>
                    <p class="text-lg text-gray-300 mb-8">
                        Join developers using Rwanda Geo for location intelligence.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" class="bg-white text-black hover:bg-gray-100 rounded-2xl px-6 py-3 font-medium flex items-center gap-2">
                            <Zap class="w-4 h-4" />
                            Start Building
                        </Button>
                        <Button variant="outline" size="lg" class="border-white text-white hover:bg-white hover:text-black rounded-2xl px-6 py-3 font-medium">
                            View Documentation
                        </Button>
                    </div>
                </div>
            </section>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Custom animations and styles */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>