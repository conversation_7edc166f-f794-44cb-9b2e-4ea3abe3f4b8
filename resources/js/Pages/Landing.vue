<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, MapPin, Globe } from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');

const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);


// Mouse tracking for interactive character
const mousePosition = ref({ x: 0, y: 0 });
const isTyping = ref(false);
const eyeDirection = ref({ x: 0, y: 0 });
const headRotation = ref(0);

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);



// Feature card data
const features = ref([
    {
        title: "Real-Time Tracking",
        description: "Track assets with high-precision GPS for instant location updates.",
        list: [
            "Low-latency updates",
            "High-precision tracking",
            "Real-time monitoring"
        ],
        image: "/images/tracking.jpg"
    },
    {
        title: "Geofencing Precision",
        description: "Set accurate boundaries using Rwanda’s administrative data.",
        list: [
            "Less than 1m accuracy",
            "Complete boundary data",
            "Customizable zones"
        ],
        image: "/images/geofencing.jpg"
    }
]);

// Configuration
const languages = [
    { code: 'rw', name: 'Kinyarwanda' },
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
];



// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu mu Rwanda...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux au Rwanda...',
}[selectedLanguage.value] || 'Search locations...'));

// --- Functions ---
const performSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });

        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);



const clearSearch = (resetQuery = true) => {
    if (resetQuery) {
        searchQuery.value = '';
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};



// Mouse tracking functions
const updateMousePosition = (event) => {
    mousePosition.value = { x: event.clientX, y: event.clientY };

    // Calculate eye direction based on mouse position
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const deltaX = (event.clientX - centerX) / centerX;
    const deltaY = (event.clientY - centerY) / centerY;

    eyeDirection.value = {
        x: Math.max(-1, Math.min(1, deltaX * 0.3)),
        y: Math.max(-1, Math.min(1, deltaY * 0.3))
    };

    // Calculate head rotation
    headRotation.value = deltaX * 5; // Max 5 degrees rotation
};

const handleTyping = () => {
    isTyping.value = true;
    setTimeout(() => {
        isTyping.value = false;
    }, 1000);
};

// --- Lifecycle ---
onMounted(() => {
    document.addEventListener('mousemove', updateMousePosition);
});

onUnmounted(() => {
    document.removeEventListener('mousemove', updateMousePosition);
});

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    handleTyping();
    performSearch(newQuery, selectedLanguage.value, 'all');
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, newLang, 'all');
    }
});
</script>

<template>
    <Head title="Rwanda Geo - Professional Geocoding Platform" />
    <AppLayout>
        <!-- Main Container -->
        <div class="min-h-screen bg-white">
            <!-- Hero Section -->
            <section class="min-h-screen bg-white py-20 px-4 sm:px-6 lg:px-8">
                <div class="max-w-7xl mx-auto h-full">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center min-h-[80vh]">

                        <!-- Section 1: Interactive Character -->
                        <div class="flex justify-center">
                            <div class="relative">
                                <svg
                                    class="w-40 h-40 text-black transition-transform duration-300 ease-out"
                                    :style="{ transform: `rotate(${headRotation}deg)` }"
                                    viewBox="0 0 100 100"
                                    fill="none"
                                >
                                    <!-- Head -->
                                    <circle cx="50" cy="40" r="25" stroke="currentColor" stroke-width="2" fill="white"/>

                                    <!-- Eyes -->
                                    <circle
                                        :cx="42 + eyeDirection.x * 2"
                                        :cy="35 + eyeDirection.y * 2"
                                        r="3"
                                        fill="currentColor"
                                        class="transition-all duration-200"
                                    />
                                    <circle
                                        :cx="58 + eyeDirection.x * 2"
                                        :cy="35 + eyeDirection.y * 2"
                                        r="3"
                                        fill="currentColor"
                                        class="transition-all duration-200"
                                    />

                                    <!-- Mouth -->
                                    <path
                                        :d="isTyping ? 'M 45 48 Q 50 52 55 48' : 'M 45 48 Q 50 45 55 48'"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        fill="none"
                                        class="transition-all duration-300"
                                    />

                                    <!-- Body -->
                                    <rect x="40" y="65" width="20" height="30" stroke="currentColor" stroke-width="2" fill="white" rx="10"/>
                                </svg>

                                <!-- Floating elements around character -->
                                <div class="absolute -top-4 -right-4 animate-bounce">
                                    <MapPin class="w-6 h-6 text-black" />
                                </div>
                                <div class="absolute -bottom-2 -left-4 animate-pulse">
                                    <Globe class="w-5 h-5 text-gray-600" />
                                </div>
                            </div>
                        </div>

                        <!-- Section 2: Main Heading -->
                        <div class="text-center">
                            <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-black tracking-tight">
                                Rwanda Map Platform
                            </h1>
                        </div>

                        <!-- Section 3: Search Card -->
                        <div class="flex justify-center">
                            <Card class="border-2 border-black rounded-3xl bg-white w-full max-w-md">
                                <CardHeader class="pb-4">
                                    <div class="text-center">
                                        <CardTitle class="text-xl font-bold text-black">Search Rwanda</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent class="space-y-4">
                                    <!-- Simple Search Input -->
                                    <div class="relative">
                                        <Input
                                            v-model="searchQuery"
                                            :placeholder="getPlaceholderText"
                                            class="h-14 pl-12 pr-4 text-lg border-2 border-gray-200 rounded-2xl focus:border-black focus:ring-0"
                                        />
                                        <Search class="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                    </div>

                                    <!-- Language Selection -->
                                    <div>
                                        <Select v-model="selectedLanguage">
                                            <SelectTrigger class="h-12 border-2 border-gray-200 rounded-2xl">
                                                <SelectValue placeholder="Select language" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="lang in languages" :key="lang.code" :value="lang.code">
                                                    {{ lang.name }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <!-- Search Results -->
                                    <div v-if="isLoading || hasResults || error" class="space-y-3">
                                        <!-- Loading State -->
                                        <div v-if="isLoading" class="flex items-center justify-center py-4">
                                            <div class="flex items-center space-x-2">
                                                <div class="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                                                <span class="text-gray-600 text-sm">Searching...</span>
                                            </div>
                                        </div>

                                        <!-- Error State -->
                                        <div v-if="error" class="p-3 bg-red-50 border border-red-200 rounded-xl">
                                            <p class="text-red-700 text-sm">{{ error }}</p>
                                        </div>

                                        <!-- Results -->
                                        <div v-if="hasResults && !isLoading" class="space-y-2">
                                            <p class="text-xs text-gray-500 text-center">
                                                {{ totalResults }} location{{ totalResults !== 1 ? 's' : '' }} found
                                            </p>

                                            <div class="max-h-48 overflow-y-auto space-y-2">
                                                <template v-for="(type, typeKey) in searchResults" :key="typeKey">
                                                    <div v-if="type.length > 0">
                                                        <div v-for="result in type.slice(0, 2)" :key="result.id"
                                                             class="p-3 bg-gray-50 border border-gray-200 rounded-xl hover:border-black transition-colors cursor-pointer">
                                                            <div class="space-y-1">
                                                                <p class="font-medium text-black text-sm">{{ getDisplayName(result) }}</p>
                                                                <p v-if="result.address" class="text-xs text-gray-600">
                                                                    {{ result.address }}
                                                                </p>
                                                                <p class="text-xs text-gray-500">
                                                                    {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>
                           

        </div>
    </AppLayout>
</template>

<style scoped>
/* Custom animations and styles */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>